import OpenAI from "openai";

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY || "demo-key-for-testing",
  dangerouslyAllowBrowser: true, // Note: In production, use a backend proxy
});

export const getAIEdit = async (text: string, editType: string) => {
  try {
    let prompt = "";

    switch (editType) {
      case "shorten":
        prompt = `Please shorten the following text while keeping the main meaning intact:\n\n"${text}"`;
        break;
      case "lengthen":
        prompt = `Please expand and elaborate on the following text with more details and context:\n\n"${text}"`;
        break;
      case "table":
        prompt = `Convert the following text into a well-formatted markdown table:\n\n"${text}"`;
        break;
      case "improve":
        prompt = `Please improve the following text by fixing grammar, enhancing clarity, and making it more engaging:\n\n"${text}"`;
        break;
      default:
        prompt = `Please improve the following text:\n\n"${text}"`;
    }

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content:
            "You are a helpful writing assistant. Provide clear, concise edits that improve the text. Return only the edited text without explanations unless specifically asked.",
        },
        {
          role: "user",
          content: prompt,
        },
      ],
      max_tokens: 500,
      temperature: 0.7,
    });

    return response.choices[0].message.content?.trim() || "";
  } catch (error) {
    console.error("AI edit error:", error);
    throw error;
  }
};

export const getChatResponse = async (message, conversationHistory = []) => {
  try {
    // Check if the message is asking for editor modifications
    const editorKeywords = [
      "insert",
      "add",
      "write",
      "create",
      "generate",
      "fix",
      "correct",
      "improve",
      "edit",
      "modify",
      "replace",
    ];
    const isEditorRequest = editorKeywords.some((keyword) =>
      message.toLowerCase().includes(keyword)
    );

    const systemPrompt = `You are an AI assistant integrated with a collaborative text editor. You can:
1. Have normal conversations and answer questions
2. Modify the editor content directly when requested

When the user asks you to modify, insert, or create content for the editor, respond with:
- A conversational response explaining what you're doing
- If you're inserting/creating content, include it in your response

For editor modifications, be helpful and creative. You can write articles, fix grammar, create lists, generate content, etc.

Current conversation context: The user is working in a collaborative editor and may ask you to help with their document.`;

    const messages = [
      { role: "system", content: systemPrompt },
      ...conversationHistory.slice(-10).map((msg) => ({
        role: msg.type === "user" ? "user" : "assistant",
        content: msg.content,
      })),
      { role: "user", content: message },
    ];

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages,
      max_tokens: 800,
      temperature: 0.7,
    });

    const content = response.choices[0].message.content.trim();

    // Determine if this should modify the editor
    let action = null;
    let editorContent = null;

    if (isEditorRequest) {
      // Extract content that should go into the editor
      // This is a simple approach - in production, you might want more sophisticated parsing
      if (
        message.toLowerCase().includes("insert") ||
        message.toLowerCase().includes("add") ||
        message.toLowerCase().includes("write")
      ) {
        action = "insert";
        // Try to extract content from the response that looks like it should be inserted
        const lines = content.split("\n");
        const contentLines = lines.filter(
          (line) =>
            !line.toLowerCase().includes("i'll") &&
            !line.toLowerCase().includes("here's") &&
            !line.toLowerCase().includes("i've") &&
            line.trim().length > 0
        );

        if (contentLines.length > 0) {
          editorContent = contentLines.join("\n");
        }
      }
    }

    return {
      content,
      action,
      editorContent,
    };
  } catch (error) {
    console.error("Chat error:", error);
    throw error;
  }
};

export const searchWeb = async (query) => {
  try {
    // For demo purposes, we'll use a simple approach
    // In production, you'd integrate with a proper search API like Tavily, Serper, etc.
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content:
            "You are a web search assistant. Provide helpful information based on the search query. Since you cannot actually search the web in real-time, provide the most relevant and up-to-date information you know about the topic.",
        },
        {
          role: "user",
          content: `Search for: ${query}`,
        },
      ],
      max_tokens: 600,
      temperature: 0.7,
    });

    return {
      results: [
        {
          title: `Information about: ${query}`,
          content: response.choices[0].message.content.trim(),
          url: "#",
        },
      ],
    };
  } catch (error) {
    console.error("Web search error:", error);
    throw error;
  }
};
